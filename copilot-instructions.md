# Copilot Instructions for DSTweaks Plugin

## Project Overview
This is a custom WordPress plugin named **DSTweaks**, built for an automobile dealership website. It provides:
- RTL layout fixes (especially for Persian RTL compatibility)
- Car-related features
- Shortcodes for front-end functionality

## Languages Used
- PHP (main plugin logic and WordPress hooks)
- HTML/CSS (output and styling)
- JavaScript (client-side interactivity)

## Code Conventions
- Follow the structure and conventions already present in the plugin.
- Use WordPress-recommended practices for:
  - Enqueuing scripts and styles
  - Using `wp_nonce_field()` and `check_admin_referer()` for secure forms
  - Sanitizing and escaping data (`sanitize_text_field()`, `esc_html()`, etc.)

## Folder Structure
- `DSTweaks/Modules/`: Contains individual modules for plugin functionality.
  - `Cars/`: Handles car-related features.
  - `PersianRTLFixes/`: Contains RTL fixes.
  - `Shortcodes/`: Provides reusable shortcodes.
- `DSTweaks/Modules/manifest.json`: Contains SHA256 hashes of module files for integrity validation. This must be updated when any module is changed.

## Security Guidelines
- Do not allow direct access to PHP files (use `defined('ABSPATH') || exit;`).
- Use the `manifest.json` file to verify module file integrity against tampering or injection.
- Follow WordPress coding standards and security best practices.
- No sensitive data should be stored or logged.

## Behavior Expectations
- When creating new modules, register them via the plugin's existing loader pattern.
- Avoid adding functionality outside of the defined modular structure.

## Internationalization (i18n)
- All front-end and admin-facing strings **must** be translation-ready.
- Use WordPress i18n functions for all static text:
  - `__()`, `_e()`, `_x()` for PHP
  - `wp.i18n.__()` or `wp.i18n._x()` for JavaScript
- Text domain: `dstweaks`
- Load the plugin text domain using `load_plugin_textdomain()` in the main plugin file.
- Avoid hardcoded strings — every label, button, message, and interface element should use a translation function.

## Languages Supported
- English (default)
- Persian (Farsi) — RTL language
