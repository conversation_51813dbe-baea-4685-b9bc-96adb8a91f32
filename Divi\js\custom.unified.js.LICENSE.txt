/*!
* SmoothScroll for websites v1.2.1
* Licensed under the terms of the MIT license.
*
* People involved
* - <PERSON><PERSON><PERSON><PERSON> (maintainer)
* - <PERSON>     (Pulse Algorithm)
*/

/*!
* Waypoints - 4.0.0
* Copyright © 2011-2015 <PERSON>
* Licensed under the MIT license.
* https://github.com/imakewebthings/waypoints/blog/master/licenses.txt
*/

/*!
* easyPieChart
* Lightweight plugin to render simple, animated and retina optimized pie charts
*
* <AUTHOR> <<EMAIL>> (http://robert-flei<PERSON>mann.de)
* @version 2.1.5
*/

/*!
* jQuery Mobile v1.4.5
* Copyright 2010, 2014 jQuery Foundation, Inc.
* jquery.org/license
*/

/*!
* jQuery hashchange event - v1.3 - 7/21/2010
* http://benalman.com/projects/jquery-hashchange-plugin/
*
* Copyright (c) 2010 "Cowboy" Ben Alman
* Dual licensed under the MIT and GPL licenses.
* http://benalman.com/about/license/
*/

/*! ET custom.js */

/*! ET et_shortcodes_frontend.js */

/*! This minified app bundle contains open source software from several third party developers. Please review CREDITS.md in the root directory or LICENSE.md in the current directory for complete licensing, copyright and patent information. This file and the included code may not be redistributed without the attributions listed in LICENSE.md, including associate copyright notices and licensing information. */
