<?php
/**
 * Module Name: DSTweaks Shortcodes Module
 * Description: A module for defining custom shortcodes in DSTweaks plugin.
 * Version: 5.4.0
 * Author: <PERSON><PERSON><PERSON>
 * Author URI: https://dsdev.ir
 * License: GPL2
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: dstweaks
 */

// Security: Prevent direct file access
defined('ABSPATH') || exit;

class DSTweaks_Shortcodes_Module {

    public function __construct() {
        add_action('init', array($this, 'register_shortcodes'));
    }

    // Register custom shortcodes
    public function register_shortcodes() {
        if (!shortcode_exists('greeting')) {
            add_shortcode('greeting', array($this, 'greeting_shortcode'));
            $GLOBALS['dstweaks_modules']['shortcodes']['messages'][]= [
                'type' => 'good',
                'message' => sprintf(
                    /* Translators: 1: [greetings] output */
                    __('[greeting {name}] = %1$s<br>Outputs a localized greeting message.<br>"name" attribute is optional, omit to output current user\'s name.<br>', 'dstweaks'), 
                    do_shortcode('[greeting]')
                )
            ];
        }
        if (!shortcode_exists('current_year')) {
            add_shortcode('current_year', array($this, 'current_year_shortcode'));
            $GLOBALS['dstweaks_modules']['shortcodes']['messages'][]= [
                'type' => 'good',
                'message' => sprintf(
                    /* Translators: 1: [current_year] output */
                    __('[current_year] = %1$s<br>Outputs localized current year.<br>', 'dstweaks'), 
                    do_shortcode('[current_year]')
                )
            ];
        }
        if (!shortcode_exists('site_url')) {
            add_shortcode('site_url', array($this, 'get_site_url_shortcode'));
            $GLOBALS['dstweaks_modules']['shortcodes']['messages'][]= [
                'type' => 'good',
                'message' => sprintf(
                    /* Translators: 1: [site_url] output */
                    __('[site_url] = %1$s<br>Outputs this website address.<br>', 'dstweaks'), 
                    do_shortcode('[site_url]')
                )
            ];
        }
        if (!shortcode_exists('page_title')) {
            add_shortcode('page_title', array($this, 'page_title_shortcode'));
            $GLOBALS['dstweaks_modules']['shortcodes']['messages'][]= [
                'type' => 'good',
                'message' => sprintf(
                    __('[page_title]<br>Outputs current page title.<br>', 'dstweaks')
                )
            ];
        }
        if (!shortcode_exists('content_by_title')) {
            add_shortcode('content_by_title', array($this, 'content_by_title_shortcode'));
            $GLOBALS['dstweaks_modules']['shortcodes']['messages'][]= [
                'type' => 'good',
                'message' => sprintf(
                    __('[content_by_title title {true/false}]<br>Outputs contents of a page using it\'s title.<br>The second attribute is optional, omit to output page content after applying some filters to make it safe.<br>', 'dstweaks')
                )
            ];
        }
        if (!shortcode_exists('post_loop')) {
            add_shortcode('post_loop', array($this, 'post_loop_shortcode'));
        }
        if (!shortcode_exists('post_loop_filters')) {
            add_shortcode('post_loop_filters', array($this, 'post_loop_filters_shortcode'));
        }
    }

    // Shortcode: [greeting {name value}] | Outputs a localized greeting message with optional name value. Omit to output current user\'s name.
    public function greeting_shortcode($func_args) {
        if (count($func_args) == 0) {
            $current_user = wp_get_current_user();
            $func_args[0] = $current_user->exists() ? $current_user->display_name : __('Guest', 'dstweaks');
        }
    
        return sprintf(
            /* Translators: %s: visitor's name */
            __('Hello, %s!', 'dstweaks'),
            wp_kses_post($func_args[0])
        );
    }

    // Helper function to convert Georgian date to Jalali date.
    public function gregorian_to_jalali($year, $month = 1, $day = 1) {
        $g_d_m = [0, 31, (function($y){ return (($y % 4 == 0 && $y % 100 != 0) || ($y % 400 == 0)) ? 29 : 28; }), 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
        $gy = $year - 1600;
        $gm = $month - 1;
        $gd = $day - 1;

        $g_day_no = 365 * $gy + intval(($gy + 3) / 4) - intval(($gy + 99) / 100) + intval(($gy + 399) / 400);
        for ($i = 0; $i < $gm; ++$i)
            $g_day_no += is_callable($g_d_m[$i + 1]) ? $g_d_m[$i + 1]($year) : $g_d_m[$i + 1];
        $g_day_no += $gd;

        $j_day_no = $g_day_no - 79;

        $j_np = intval($j_day_no / 12053);
        $j_day_no %= 12053;

        $jy = 979 + 33 * $j_np + 4 * intval($j_day_no / 1461);
        $j_day_no %= 1461;

        if ($j_day_no >= 366) {
            $jy += intval(($j_day_no - 1) / 365);
        }

        return $jy;
    }
    // Shortcode: [current_year] | Outputs localized current year.
    public function current_year_shortcode() {
        if (get_locale() === 'fa_IR') {
            return $this->gregorian_to_jalali(date('Y'));
        }
        return date('Y');
    }

    // Shortcode: [site_url] | Outputs this website url address.
    public function get_site_url_shortcode() {
        return site_url();
    }

    // Shortcode: [page_title] | Outputs current page title.
    public function page_title_shortcode() {
        return get_the_title();
    }

    // Helper function to determine a boolean value based on a string
    private function to_bool($value) {
        $value = strtolower(trim($value));

        return in_array($value, ['1', 'true', 'yes', 'on'], true) ? true
            : (in_array($value, ['0', 'false', 'no', 'off'], true) ? false : null);
    }
    // Shortcode: [content_by_title title {true/false}] | Outputs contents of a page using it\'s title.
    public function content_by_title_shortcode($func_args) {
        // Checking for correct number of arguments
        if (!is_array($func_args)) return;
        if (count($func_args) == 0) return;
        // Extracting the arguments
        $title = $func_args[0];
        $filters = $this->to_bool($func_args[1]);
        // Query the title
        $query = new WP_Query([
            'title'          => $title,
            'post_type'      => 'any',
            'posts_per_page' => 1,
            'post_status'    => 'any',
        ]);
        
        if ($query->have_posts()) {
            $query->the_post();
            $post_id = get_the_ID();
            $is_divi = get_post_meta($post_id, '_et_pb_use_builder', true) === 'on';
            $content = get_the_content();
            if ($is_divi && function_exists('et_builder_render_layout')) {
                $content = et_builder_render_layout($content);
            } else {
                if ($filters) {
                    $content = apply_filters('the_content', $content);
                    $content = do_shortcode($content);
                }
            }
            wp_reset_postdata();
            return $content;
        }
        return '';
    }

    // Shortcode: [post_loop theme posts_per_page post_type elements_list loop_id]
    public function post_loop_shortcode($func_args) {
        $theme          = (isset($func_args[0]) && $func_args[0] != '')                            ? esc_html($func_args[0]) : 'blog';
        $posts_per_page = (isset($func_args[1]) && filter_var($func_args[1], FILTER_VALIDATE_INT)) ? esc_html($func_args[1]) : '10';
        $post_type      = (isset($func_args[2]) && $func_args[2] != '')                            ? esc_html($func_args[2]) : 'post';
        $elements_list  = (isset($func_args[3]) && $func_args[3] != '')                            ? esc_html($func_args[3]) : 'title,image';
        $elements_list  = array_map('trim', explode(',', $func_args[3]));
        $loop_id        = (isset($func_args[4]) && $func_args[4] != '')                            ? esc_html($func_args[4]) : wp_unique_id('loop_');

        // Caching output for performance
        $cache_key = 'dstweaks_post_loop_' . md5(serialize([$theme, $posts_per_page, $post_type, $elements_list, $loop_id]));
        $cached_output = get_transient($cache_key);
        if ($cached_output !== false) {
            return $cached_output;
        }

        $query = new WP_Query(([
            'posts_per_page' => (int) $posts_per_page,
            'post_type'      => $post_type,
            'status'         => 'published'
        ]));

        if ($query->have_posts()) {
            $car_module = null;
            if ($post_type === 'project' && isset($GLOBALS['dstweaks_modules']['cars']['obj'])) {
                $car_module = $GLOBALS['dstweaks_modules']['cars']['obj'];
            }
            $translatable_fields = ['title', 'image', 'date', 'author', 'category', 'brand', 'tags'];

            ob_start();
            echo '<div class="post-loop post-loop-' . esc_attr($theme) . '" id="' . esc_attr($loop_id) . '">';

            // ***** Theme: Table *****
            if ($theme === 'table') { 
                echo '<div class="post-table-wrapper">';
                // Table Header
                    echo '<table>';
                    echo '<thead><tr>';
                    foreach ($elements_list as $element) {
                        if ($car_module) {
                            if ($element === 'category')    $element = 'brand';
                            $info = $car_module->get_field_info_by_slug($element);
                            $label = $info && isset($info['label_trans']) ? __($info['label_trans'], 'dstweaks') : ucfirst(str_replace('_', ' ', $element));
                        } elseif (in_array($element, $translatable_fields, true)) {
                            $label = __(ucfirst(str_replace('_', ' ', $element)), 'dstweaks');
                        } else {
                            $label = ucfirst(str_replace('_', ' ', $element));
                        }
                        echo '<th>' . esc_html($label) . '</th>';
                    }
                    echo '</tr></thead>';
                // Table Body
                    echo '<tbody>';
                    while ($query->have_posts()) {
                        $query->the_post();
                        echo '<tr class="post-item-wrapper">';
                        // Item
                        foreach ($elements_list as $element) {
                            echo '<td data-element="' . $element . '">';
                            switch ($element) {
                                case 'image':
                                    echo '<div class="post-featured-image image" data-type="image">';
                                    if (has_post_thumbnail()) {
                                        $alt = get_the_title();
                                        echo get_the_post_thumbnail(null, 'thumbnail', ['alt' => esc_attr($alt)]);
                                    }
                                    echo '</div>';
                                    break;
                                case 'title':
                                    echo '<div class="post-title text" data-type="text">';
                                    echo '<a href="' . esc_url(get_permalink()) . '">' . esc_html(get_the_title()) . '</a>';
                                    echo '</div>';
                                    break;
                                case 'date':
                                    echo '<div class="post-published-date date" data-type="date">';
                                    echo get_the_date();
                                    echo '</div>';
                                    break;
                                case 'author':
                                    echo '<div class="post-author text" data-type="text">';
                                    echo get_the_author();
                                    echo '</div>';
                                    break;
                                case 'tags':
                                    echo '<div class="post-tags text" data-type="csv">';
                                    if ($post_type === 'post') {
                                        $value = get_the_tags();
                                        if (!empty($value)) echo implode(', ', wp_list_pluck($value, 'name'));
                                    } else {
                                        if ($post_type === 'project')
                                            $terms = get_the_terms(get_the_ID(), 'project_tag');
                                        else
                                            $terms = get_the_terms(get_the_ID(), 'post_tag');
                                        if (!empty($terms) && !is_wp_error($terms)) {
                                            echo implode(', ', wp_list_pluck($terms, 'name'));
                                        }
                                    }
                                    echo '</div>';
                                    break;
                                case 'category':
                                    echo '<div class="post-category text" data-type="csv">';
                                    if ($post_type === 'post') {
                                        $value = get_the_category();
                                        if (!empty($value)) echo implode(', ', wp_list_pluck($value, 'name'));
                                    } else {
                                        if ($post_type === 'project')
                                            $terms = get_the_terms(get_the_ID(), 'project_category');
                                        else
                                            $terms = get_the_terms(get_the_ID(), 'category');
                                        if (!empty($terms) && !is_wp_error($terms)) {
                                            echo implode(', ', wp_list_pluck($terms, 'name'));
                                        }
                                    }
                                    echo '</div>';
                                    break;
                                default:
                                    $value = get_post_meta(get_the_ID(), $element, true);
                                    if (!isset($value) || $value == '') break;
                                    $type = '';
                                    if ($car_module) { //if its car_module, field type is known
                                        $info = $car_module->get_field_info_by_slug($element);
                                        if ($info && isset($info['type'])) $type = $info['type'];
                                    } else { //unknown field - trying to detect its type
                                        if (is_numeric($value) && get_post_mime_type($value)) {
                                            $mime_type = get_post_mime_type($value);
                                            if (str_starts_with($mime_type, 'image/')) {
                                                $type = 'image';
                                            } else {
                                                $type = 'file';
                                            }
                                        } elseif (strpos($value, ',') !== false && preg_match('/^\d+(,\d+)+$/', $value)) {
                                            $type = 'gallery';
                                        } elseif (strlen($value) > 100) {
                                            $type = 'textarea';
                                        } else {
                                            $type = 'text';
                                        }
                                    }
                                    echo '<div class="post-meta ' . esc_attr($element) . ' ' . $type . '" data-type="' . $type . '">';
                                    switch ($type) {
                                        case 'number':
                                        case 'text':
                                            echo esc_html($value);
                                            break;
                                        case 'textarea':
                                            echo wpautop(esc_html($value));
                                            break;
                                        case 'image':
                                            $alt = get_post_meta($value, '_wp_attachment_image_alt', true);
                                            if (!$alt) $alt = get_the_title();
                                            echo wp_get_attachment_image($value, 'thumbnail', false, ['alt' => esc_attr($alt)]);
                                            break;
                                        case 'gallery':
                                            $ids = array_filter(array_map('absint', explode(',', $value)));
                                            foreach ($ids as $id) {
                                                if (wp_attachment_is_image($id)) {
                                                    $alt = get_post_meta($id, '_wp_attachment_image_alt', true);
                                                    if (!$alt) $alt = get_the_title();
                                                    echo wp_get_attachment_image($id, 'thumbnail', false, ['alt' => esc_attr($alt)]);
                                                }
                                            }
                                            break;
                                        case 'file':
                                            $file_url = wp_get_attachment_url($value);
                                            $file_caption = wp_get_attachment_caption($value);
                                            echo '<a href="' . esc_url($file_url) . '" target="_blank" download="' . esc_attr($file_caption) . '">' . esc_html__('Download File', 'dstweaks') . '</a>';
                                            break;
                                        default:
                                            echo esc_html($value);
                                    }
                                    echo '</div>';
                            }
                            echo '</td>';
                        }
                        echo '</tr>';
                    }
                    echo '</tbody>';
                    echo '</table>';
                echo '</div>';
            }            
            // ***** Theme: Blog *****
            elseif ($theme === 'blog') {
                echo '<div class="post-blog-wrapper">';
                while ($query->have_posts()) {
                    $query->the_post();
                    echo '<div class="post-item-wrapper">';
                    // Item
                    foreach ($elements_list as $element) {
                        echo '<td data-element="' . $element . '">';
                        switch ($element) {
                            case 'image':
                                echo '<div class="post-featured-image image" data-type="image">';
                                if (has_post_thumbnail()) {
                                    $alt = get_the_title();
                                    echo get_the_post_thumbnail(null, 'thumbnail', ['alt' => esc_attr($alt)]);
                                }
                                echo '</div>';
                                break;
                            case 'title':
                                echo '<div class="post-title text" data-type="text">';
                                echo '<a href="' . esc_url(get_permalink()) . '">' . esc_html(get_the_title()) . '</a>';
                                echo '</div>';
                                break;
                            case 'date':
                                echo '<div class="post-published-date date" data-type="date">';
                                echo get_the_date();
                                echo '</div>';
                                break;
                            case 'author':
                                echo '<div class="post-author text" data-type="text">';
                                echo get_the_author();
                                echo '</div>';
                                break;
                            case 'tags':
                                echo '<div class="post-tags text" data-type="csv">';
                                if ($post_type === 'post') {
                                    $value = get_the_tags();
                                    if (!empty($value)) echo implode(', ', wp_list_pluck($value, 'name'));
                                } else {
                                    if ($post_type === 'project')
                                        $terms = get_the_terms(get_the_ID(), 'project_tag');
                                    else
                                        $terms = get_the_terms(get_the_ID(), 'post_tag');
                                    if (!empty($terms) && !is_wp_error($terms)) {
                                        echo implode(', ', wp_list_pluck($terms, 'name'));
                                    }
                                }
                                echo '</div>';
                                break;
                            case 'category':
                                echo '<div class="post-category text" data-type="csv">';
                                if ($post_type === 'post') {
                                    $value = get_the_category();
                                    if (!empty($value)) echo implode(', ', wp_list_pluck($value, 'name'));
                                } else {
                                    if ($post_type === 'project')
                                        $terms = get_the_terms(get_the_ID(), 'project_category');
                                    else
                                        $terms = get_the_terms(get_the_ID(), 'category');
                                    if (!empty($terms) && !is_wp_error($terms)) {
                                        echo implode(', ', wp_list_pluck($terms, 'name'));
                                    }
                                }
                                echo '</div>';
                                break;
                            default:
                                $value = get_post_meta(get_the_ID(), $element, true);
                                if (!isset($value) || $value == '') break;
                                $type = '';
                                if ($car_module) { //if its car_module, field type is known
                                    $info = $car_module->get_field_info_by_slug($element);
                                    if ($info && isset($info['type'])) $type = $info['type'];
                                } else { //unknown field - trying to detect its type
                                    if (is_numeric($value) && get_post_mime_type($value)) {
                                        $mime_type = get_post_mime_type($value);
                                        if (str_starts_with($mime_type, 'image/')) {
                                            $type = 'image';
                                        } else {
                                            $type = 'file';
                                        }
                                    } elseif (strpos($value, ',') !== false && preg_match('/^\d+(,\d+)+$/', $value)) {
                                        $type = 'gallery';
                                    } elseif (strlen($value) > 100) {
                                        $type = 'textarea';
                                    } else {
                                        $type = 'text';
                                    }
                                }
                                echo '<div class="post-meta ' . esc_attr($element) . ' ' . $type . '" data-type="' . $type . '">';
                                switch ($type) {
                                    case 'number':
                                    case 'text':
                                        echo esc_html($value);
                                        break;
                                    case 'textarea':
                                        echo wpautop(esc_html($value));
                                        break;
                                    case 'image':
                                        $alt = get_post_meta($value, '_wp_attachment_image_alt', true);
                                        if (!$alt) $alt = get_the_title();
                                        echo wp_get_attachment_image($value, 'thumbnail', false, ['alt' => esc_attr($alt)]);
                                        break;
                                    case 'gallery':
                                        $ids = array_filter(array_map('absint', explode(',', $value)));
                                        foreach ($ids as $id) {
                                            if (wp_attachment_is_image($id)) {
                                                $alt = get_post_meta($id, '_wp_attachment_image_alt', true);
                                                if (!$alt) $alt = get_the_title();
                                                echo wp_get_attachment_image($id, 'thumbnail', false, ['alt' => esc_attr($alt)]);
                                            }
                                        }
                                        break;
                                    case 'file':
                                        $file_url = wp_get_attachment_url($value);
                                        $file_caption = wp_get_attachment_caption($value);
                                        echo '<a href="' . esc_url($file_url) . '" target="_blank" download="' . esc_attr($file_caption) . '">' . esc_html__('Download File', 'dstweaks') . '</a>';
                                        break;
                                    default:
                                        echo esc_html($value);
                                }
                                echo '</div>';
                        }
                        echo '</td>';
                    }
                    echo '</div>';
                }
                echo '</div>';
            }

            wp_reset_postdata();
            echo '</div>';
            $output = ob_get_clean();
            set_transient($cache_key, $output, 12 * HOUR_IN_SECONDS); // cache for 12 hours
            return $output;
        }
    }

    // Shortcode: [post_loop_filters elements_list loop_id]
    public function post_loop_filters_shortcode($func_args) {
        if (!is_array($func_args) || count($func_args) < 2) return '';
        $elements_list = array_map('trim', explode(',', $func_args[0]));
        $loop_id = esc_attr($func_args[1]);
        ob_start();
        echo '<form class="post-loop-filters" data-loop-id="' . $loop_id . '" onsubmit="return false;">';
        foreach ($elements_list as $element) {
            echo '<div class="filter-item type-text" data-filter-element="' . esc_attr($element) . '" data-type="text">';
            echo '<input type="text" class="filter-input" name="filter_' . esc_attr($element) . '" />';
            echo '<button type="button" class="remove-filter" title="' . esc_attr__('Remove filter', 'dstweaks') . '">&times;</button>';
            echo '</div>';
        }
        echo '</form>';
        ?>
        <style>
            /* Price Range Slider Styles */
            .price-range-wrapper {
                display: inline-block;
                padding: 15px;
                margin: 10px 0;
                background: #f9f9f9;
                border: 1px solid #ddd;
                border-radius: 8px;
                min-width: 300px;
                vertical-align: middle;
            }
            .price-range-values {
                font-size: 14px;
                font-weight: 600;
                color: #333;
                text-align: center;
                margin-bottom: 10px;
                padding: 5px;
                background: #fff;
                border-radius: 4px;
                border: 1px solid #e0e0e0;
            }
            .price-range-slider {
                margin: 15px 0;
            }
            /* Custom styling for ion.rangeSlider */
            .dstweaks-price-slider .irs--round .irs-bar {
                background: linear-gradient(to right, #2271b1, #135e96);
                height: 6px;
            }
            .dstweaks-price-slider .irs--round .irs-handle {
                width: 20px;
                height: 20px;
                background: #2271b1;
                border: 3px solid #fff;
                box-shadow: 0 2px 6px rgba(0,0,0,0.2);
            }
            .dstweaks-price-slider .irs--round .irs-handle:hover {
                background: #135e96;
            }
            .dstweaks-price-slider .irs--round .irs-from,
            .dstweaks-price-slider .irs--round .irs-to,
            .dstweaks-price-slider .irs--round .irs-single {
                background: #2271b1;
                color: #fff;
                font-weight: 600;
                padding: 3px 8px;
                border-radius: 4px;
            }
            .dstweaks-price-slider .irs--round .irs-grid-text {
                font-size: 11px;
                color: #666;
            }
        </style>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                var filterForms = document.querySelectorAll('.post-loop-filters[data-loop-id="<?php echo $loop_id; ?>"]');
                var loop = document.getElementById('<?php echo $loop_id; ?>');
                if (!loop) return;
                filterForms.forEach(function(filters) {
                    filters.querySelectorAll('.filter-item').forEach(function(item) {
                        var element = item.getAttribute('data-filter-element');
                        var loopField = loop.querySelector('[data-element="' + element + '"] [data-type]');
                        var type = loopField ? loopField.getAttribute('data-type') : 'text';
                        item.setAttribute('data-type', type);
                        item.className = 'filter-item ' + type;
                        var input = item.querySelector('.filter-input');
                        if (type === 'number') {
                            if (element === 'price') {
                                // Load jQuery if not already loaded (required for ion.rangeSlider)
                                if (!window.jQuery && !document.getElementById('dstweaks-jquery')) {
                                    var jqueryScript = document.createElement('script');
                                    jqueryScript.id = 'dstweaks-jquery';
                                    jqueryScript.src = 'https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js';
                                    document.head.appendChild(jqueryScript);
                                }

                                // Load ion.rangeSlider if not already loaded
                                if (!document.getElementById('ionrangeslider-css')) {
                                    var sliderCSS = document.createElement('link');
                                    sliderCSS.id = 'ionrangeslider-css';
                                    sliderCSS.rel = 'stylesheet';
                                    sliderCSS.href = 'https://cdn.jsdelivr.net/npm/ion-rangeslider@2.3.1/css/ion.rangeSlider.min.css';
                                    document.head.appendChild(sliderCSS);
                                }

                                if (!document.getElementById('ionrangeslider-js')) {
                                    var sliderScript = document.createElement('script');
                                    sliderScript.id = 'ionrangeslider-js';
                                    sliderScript.src = 'https://cdn.jsdelivr.net/npm/ion-rangeslider@2.3.1/js/ion.rangeSlider.min.js';
                                    document.head.appendChild(sliderScript);
                                }

                                // Create range wrapper
                                var rangeWrapper = document.createElement('div');
                                rangeWrapper.className = 'price-range-wrapper';

                                // Create slider input
                                var sliderInput = document.createElement('input');
                                sliderInput.type = 'text';
                                sliderInput.className = 'price-range-slider';
                                sliderInput.setAttribute('data-slider-initialized', 'false');

                                // Create values display
                                var valuesDisplay = document.createElement('div');
                                valuesDisplay.className = 'price-range-values';
                                valuesDisplay.textContent = '<?php echo esc_js(__("Loading price range...", "dstweaks")); ?>';

                                // Add elements to wrapper
                                rangeWrapper.appendChild(valuesDisplay);
                                rangeWrapper.appendChild(sliderInput);

                                // Replace original input with range wrapper
                                input.replaceWith(rangeWrapper);

                                // Initialize slider once ion.rangeSlider is loaded
                                function initSlider() {
                                    // Check if jQuery and ion.rangeSlider are available
                                    if (!window.jQuery || !window.jQuery.fn.ionRangeSlider) {
                                        setTimeout(initSlider, 100);
                                        return;
                                    }

                                    // Prevent double initialization
                                    if (sliderInput.getAttribute('data-slider-initialized') === 'true') {
                                        return;
                                    }

                                    // Find all price values and determine min/max
                                    var priceValues = [];
                                    loop.querySelectorAll('[data-element="price"] [data-type]').forEach(function(field) {
                                        // Remove thousand separators and parse as number
                                        var priceText = field.textContent.replace(/[^\d.-]/g, '');
                                        var price = parseFloat(priceText);
                                        if (!isNaN(price) && price > 0) {
                                            priceValues.push(price);
                                        }
                                    });

                                    if (priceValues.length === 0) {
                                        valuesDisplay.textContent = '<?php echo esc_js(__("No price data available", "dstweaks")); ?>';
                                        return;
                                    }

                                    var minPrice = Math.min(...priceValues);
                                    var maxPrice = Math.max(...priceValues);

                                    // Calculate smart step value based on price range
                                    var range = maxPrice - minPrice;
                                    var step = 1;
                                    if (range > 1000000) step = 50000;
                                    else if (range > 100000) step = 10000;
                                    else if (range > 10000) step = 1000;
                                    else if (range > 1000) step = 100;
                                    else if (range > 100) step = 10;

                                    // Initialize ion.rangeSlider
                                    window.jQuery(sliderInput).ionRangeSlider({
                                        type: "double",
                                        min: minPrice,
                                        max: maxPrice,
                                        from: minPrice,
                                        to: maxPrice,
                                        step: step,
                                        grid: true,
                                        grid_num: 4,
                                        prettify_enabled: true,
                                        prettify_separator: ",",
                                        prefix: "<?php echo esc_js((function_exists('get_woocommerce_currency_symbol') ? get_woocommerce_currency_symbol() : '$')); ?>",
                                        force_edges: true,
                                        skin: "round",
                                        hide_min_max: false,
                                        hide_from_to: false,
                                        block: false,
                                        extra_classes: "dstweaks-price-slider",
                                        onStart: function (data) {
                                            // Set filter as initially disabled
                                            item.setAttribute('data-filter-enabled', 'false');
                                            item.setAttribute('data-min', '');
                                            item.setAttribute('data-max', '');
                                            valuesDisplay.textContent = '<?php echo esc_js(__("Drag to filter by price range", "dstweaks")); ?>';
                                        },
                                        onChange: function (data) {
                                            // Enable filter when user changes values
                                            if (data.from !== data.min || data.to !== data.max) {
                                                item.setAttribute('data-filter-enabled', 'true');
                                                item.setAttribute('data-min', data.from);
                                                item.setAttribute('data-max', data.to);
                                                valuesDisplay.textContent = data.from_pretty + ' - ' + data.to_pretty;
                                            } else {
                                                item.setAttribute('data-filter-enabled', 'false');
                                                item.setAttribute('data-min', '');
                                                item.setAttribute('data-max', '');
                                                valuesDisplay.textContent = '<?php echo esc_js(__("All prices", "dstweaks")); ?>';
                                            }
                                        },
                                        onFinish: function (data) {
                                            // Apply filter when user finishes interaction
                                            setTimeout(function() {
                                                runFilters();
                                            }, 50);
                                        }
                                    });

                                    sliderInput.setAttribute('data-slider-initialized', 'true');

                                    // Store slider instance for later access
                                    item.sliderInstance = window.jQuery(sliderInput).data("ionRangeSlider");
                                }

                                initSlider();
                            } else {
                                input.setAttribute('type', 'number');
                            }
                        } else if (type === 'date') {
                            var newInput = document.createElement('input');
                            newInput.type = 'date';
                            newInput.className = 'filter-input';
                            newInput.name = input.name;
                            input.replaceWith(newInput);
                        } else if (type === 'textarea') {
                            input.setAttribute('type', 'text');
                        } else if (type === 'image' || type === 'gallery' || type === 'file') {
                            var newInput = document.createElement('input');
                            newInput.type = 'checkbox';
                            newInput.className = 'filter-toggle';
                            newInput.name = input.name;
                            input.replaceWith(newInput);
                        } else if (type === 'csv') {
                            var values = new Set();
                            loop.querySelectorAll('[data-element="' + element + '"] [data-type="csv"]').forEach(function(field) {
                                field.textContent.split(',').forEach(function(val) {
                                    var v = val.trim();
                                    if (v) values.add(v);
                                });
                            });
                            var select2 = document.createElement('select');
                            select2.className = 'filter-select select2-multiple';
                            select2.name = input.name;
                            select2.multiple = true;
                            values.forEach(function(val) {
                                var option = document.createElement('option');
                                option.value = val;
                                option.textContent = val;
                                select2.appendChild(option);
                            });
                            // Select2 requirements: load CSS/JS only once
                            if (!document.getElementById('dstweaks-select2-css')) {
                            var select2stylesheet = document.createElement("link");
                            select2stylesheet.id = 'dstweaks-select2-css';
                            select2stylesheet.rel = "stylesheet";
                            select2stylesheet.href = "https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css";
                            document.head.appendChild(select2stylesheet);
                            }
                            function attachSelect2Events(sel) {
                            sel.addEventListener('change', runFilters);
                            if (window.jQuery && window.jQuery(sel).on) {
                                window.jQuery(sel).on('select2:select', runFilters);
                                window.jQuery(sel).on('select2:unselect', runFilters);
                            } else if (window.$ && window.$(sel).on) {
                                window.$(sel).on('select2:select', runFilters);
                                window.$(sel).on('select2:unselect', runFilters);
                            }
                            }
                            if (!document.getElementById('dstweaks-select2-js')) {
                            var select2script = document.createElement("script");
                            select2script.id = 'dstweaks-select2-js';
                            select2script.src = "https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js";
                            select2script.async = false;
                            document.head.appendChild(select2script);
                            select2script.onload = function() {
                                if (window.jQuery && window.jQuery(select2).select2) {
                                window.jQuery(select2).select2();
                                attachSelect2Events(select2);
                                } else if (window.$ && window.$(select2).select2) {
                                window.$(select2).select2();
                                attachSelect2Events(select2);
                                }
                            };
                            } else {
                            setTimeout(function() {
                                if (window.jQuery && window.jQuery(select2).select2) {
                                window.jQuery(select2).select2();
                                attachSelect2Events(select2);
                                } else if (window.$ && window.$(select2).select2) {
                                window.$(select2).select2();
                                attachSelect2Events(select2);
                                }
                            }, 0);
                            }
                            input.replaceWith(select2);
                        }
                    });
                    filters.addEventListener('input', function(e) {
                        // Don't trigger on price slider elements (ion.rangeSlider handles its own events)
                        if (!e.target.closest('.price-range-wrapper') && !e.target.classList.contains('price-range-slider')) {
                            runFilters();
                        }
                    });
                    filters.addEventListener('change', function(e) {
                        // Don't trigger on price slider elements (ion.rangeSlider handles its own events)
                        if (!e.target.closest('.price-range-wrapper') && !e.target.classList.contains('price-range-slider')) {
                            runFilters();
                        }
                    });
                    filters.querySelectorAll('.remove-filter').forEach(function(btn) {
                        btn.addEventListener('click', function() {
                            var item = btn.closest('.filter-item');
                            var type = item.getAttribute('data-type');
                            var element = item.getAttribute('data-filter-element');
                            
                            if (element === 'price') {
                                // Disable the price filter
                                item.setAttribute('data-filter-enabled', 'false');
                                item.setAttribute('data-min', '');
                                item.setAttribute('data-max', '');

                                // Reset the ion.rangeSlider to its initial state
                                if (item.sliderInstance) {
                                    var sliderData = item.sliderInstance.options;
                                    item.sliderInstance.update({
                                        from: sliderData.min,
                                        to: sliderData.max
                                    });
                                }

                                // Update values display
                                var valuesDisplay = item.querySelector('.price-range-values');
                                if (valuesDisplay) {
                                    valuesDisplay.textContent = '<?php echo esc_js(__("All prices", "dstweaks")); ?>';
                                }
                            } else if (type === 'image' || type === 'gallery' || type === 'file') {
                                var toggle = item.querySelector('.filter-toggle');
                                if (toggle) toggle.checked = false;
                            } else if (type === 'csv') {
                                var select = item.querySelector('.filter-select');
                                if (select) {
                                    // Clear select2 selections using API if available
                                    if (window.jQuery && window.jQuery(select).data('select2')) {
                                        window.jQuery(select).val(null).trigger('change');
                                    } else if (window.$ && window.$(select).data('select2')) {
                                        window.$(select).val(null).trigger('change');
                                    } else if (typeof select.select2 === 'function') {
                                        select.select2('val', null);
                                    } else {
                                        Array.from(select.options).forEach(function(opt) { opt.selected = false; });
                                    }
                                }
                                var ac = item.querySelector('.filter-autocomplete');
                                if (ac) ac.value = '';
                                if (select) Array.from(select.options).forEach(function(opt) { opt.style.display = ''; });
                            } else {
                                var input = item.querySelector('.filter-input');
                                if (input) input.value = '';
                            }
                            runFilters();
                        });
                    });
                    var filterTimeout;
                    function runFilters() {
                        // Debounce to prevent rapid successive calls
                        clearTimeout(filterTimeout);
                        filterTimeout = setTimeout(function() {
                            var filtersArr = [];
                        // Combine filters from all forms for this loop
                        var allForms = document.querySelectorAll('.post-loop-filters[data-loop-id="' + loop.id + '"]');
                        allForms.forEach(function(form) {
                            form.querySelectorAll('.filter-item').forEach(function(item) {
                                var field = item.getAttribute('data-filter-element');
                                var type = item.getAttribute('data-type');
                                var value;
                                if (type === 'text' || type === 'textarea' || type === 'date') {
                                    var input = item.querySelector('.filter-input');
                                    value = input ? input.value : '';
                                    if (value !== '') {
                                        filtersArr.push({field: field, type: type, value: value});
                                    }
                                } else if (type === 'number') {
                                    if (field === 'price') {
                                        // Only apply price filter if it's enabled
                                        if (item.getAttribute('data-filter-enabled') === 'true') {
                                            var minValue = item.getAttribute('data-min');
                                            var maxValue = item.getAttribute('data-max');
                                            if (minValue !== '' || maxValue !== '') {
                                                filtersArr.push({
                                                    field: field, 
                                                    type: 'range', 
                                                    value: {
                                                        min: minValue !== '' ? parseFloat(minValue) : null,
                                                        max: maxValue !== '' ? parseFloat(maxValue) : null
                                                    }
                                                });
                                            }
                                        }
                                    } else {
                                        var input = item.querySelector('.filter-input');
                                        value = input ? input.value : '';
                                        if (value !== '') {
                                            filtersArr.push({field: field, type: type, value: value});
                                        }
                                    }
                                } else if (type === 'image' || type === 'gallery' || type === 'file') {
                                    var toggle = item.querySelector('.filter-toggle');
                                    value = toggle ? toggle.checked : false;
                                    if (value) {
                                        filtersArr.push({field: field, type: type, value: value});
                                    }
                                } else if (type === 'csv') {
                                    var select = item.querySelector('.filter-select');
                                    var selected = [];
                                    // Try jQuery select2 first
                                    if (select) {
                                        if (window.jQuery && window.jQuery(select).data('select2')) {
                                            selected = window.jQuery(select).val();
                                        } else if (window.$ && window.$(select).data('select2')) {
                                            selected = window.$(select).val();
                                        } else if (typeof select.select2 === 'function') {
                                            // If select2 is attached directly to the element
                                            selected = select.select2('val');
                                        } else {
                                            selected = Array.from(select.selectedOptions).map(function(opt) { return opt.value; });
                                        }
                                        // Ensure selected is always an array
                                        if (typeof selected === 'string') {
                                            selected = [selected];
                                        } else if (!Array.isArray(selected)) {
                                            selected = [];
                                        }
                                    }
                                    if (selected.length > 0) {
                                        filtersArr.push({field: field, type: type, value: selected});
                                    }
                                }
                            });
                        });
                        loop.querySelectorAll('.post-item-wrapper').forEach(function(item) {
                            var match = true;
                            filtersArr.forEach(function(filter) {
                                var fieldClass = '[data-element="' + filter.field + '"] [data-type]';
                                var field = item.querySelector(fieldClass);
                                if (filter.type === 'csv') {
                                    if (!field) { match = false; return; }
                                    var values = field.textContent.split(',').map(function(v) { return v.trim(); });
                                    if (!filter.value.every(function(val) { return values.includes(val); })) {
                                        match = false;
                                    }
                                } else if (filter.type === 'range') {
                                    // Remove thousand separators before parsing
                                    var value = field ? parseFloat(field.textContent.replace(/,/g, '')) : 0;
                                    if ((filter.value.min !== null && value < filter.value.min) || 
                                        (filter.value.max !== null && value > filter.value.max)) {
                                        match = false;
                                    }
                                } else if (filter.type === 'text' || filter.type === 'textarea' || filter.type === 'number' || filter.type === 'date') {
                                    var text = field ? field.textContent : '';
                                    if (text.indexOf(filter.value) === -1) {
                                        match = false;
                                    }
                                } else if (filter.type === 'image' || filter.type === 'gallery' || filter.type === 'file') {
                                    if (!field || field.innerHTML.trim() === '') {
                                        match = false;
                                    }
                                }
                            });
                            item.style.display = match ? '' : 'none';
                        });
                        }, 50); // 50ms debounce delay
                    }
                });
            });
            document.addEventListener("DOMContentLoaded", function () {
            document.querySelectorAll('.select2-multiple').forEach(function (el) {
                window.$(el).select2();
            });
            });
        </script>
        <?php
        return ob_get_clean();
    }
}

if (class_exists('DSTweaks_Shortcodes_Module') && !isset($GLOBALS['dstweaks_modules']['shortcodes']['obj'])) {
    $GLOBALS['dstweaks_modules']['shortcodes']['obj'] = new DSTweaks_Shortcodes_Module();
} else 
    $GLOBALS['dstweaks_modules']['shortcodes']['errors'] = "Instantiation failed. Couldn\'t find the required class.";
