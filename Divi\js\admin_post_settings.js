!function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=90)}({90:function(e,t){!function(e){function t(){var t=e(".editor-post-format select").length>0?"select":"check",n=e("select"===t?".editor-post-format select":'input[name="post_format"]'),r=e(".et_divi_format_setting"),o=e("#et_post_use_bg_color");e(".color-picker-hex").wpColorPicker(),n.on("change",(function(){var t=e(this);r.hide(),e(".et_divi_format_setting.et_divi_"+t.val()+"_settings").show(),o.trigger("change")})),o.on("change",(function(){var t=e(this);t.is(":visible")&&e(".et_post_bg_color_setting").toggle(t.is(":checked"))})),"select"===t?n.trigger("change"):n.filter(":checked").trigger("change")}e((function(){t()})),e(document).on("ETGBReady",(function(e){setTimeout((function(){t()}),100)}))}(jQuery)}});