<?php
/**
 * Module Name: Dynamic Contact Form
 * Description: A module for enabling shortcodes in Message settings of Divi Contact Form module
 * Version: 1.0.0
 * Author: <PERSON><PERSON><PERSON>
 * Author URI: https://dsdev.ir
 * License: GPL2
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: dstweaks
 */

// Security: Prevent direct file access
defined('ABSPATH') || exit;

add_filter('et_pb_contact_form_send_email', 'run_shortcodes_in_message_pattern_with_page_context', 10, 1);

function run_shortcodes_in_message_pattern_with_page_context($email_data) {
    if (empty($email_data['message']) || empty($email_data['module_id'])) {
        return $email_data;
    }

    // Get the module's post (page) where it was used
    $form_post = get_post($email_data['module_id']);
    if (!$form_post) {
        return $email_data;
    }

    // Backup current global post
    global $post;
    $original_post = $post;

    // Set global post to form's page to simulate page context
    $post = $form_post;
    setup_postdata($post);

    // Run shortcodes on the message
    $email_data['message'] = do_shortcode($email_data['message']);

    // Restore original post
    wp_reset_postdata();
    $post = $original_post;

    return $email_data;
}
