// Handles AJAX search for [post_loop_search] shortcode
jQuery(document).ready(function($){
    var timer;
    $(document).on('input', '.post-loop-search-input', function(){
        var $input = $(this);
        var $wrapper = $input.closest('.post-loop-search-wrapper');
        var query = $input.val();
        var data = {
            action: 'dstweaks_post_loop_search',
            search: query,
            nonce: dstweaks_post_loop_search.nonce,
            args: $wrapper.data('args')
        };
        clearTimeout(timer);
        timer = setTimeout(function(){
            $.post(dstweaks_post_loop_search.ajax_url, data, function(response){
                $wrapper.find('.post-loop-search-results').html(response.data.html);
            });
        }, 300);
    });
});
